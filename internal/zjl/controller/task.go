package controller

import (
	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gitee.com/rcztcs/zjl/internal/zjl/svc"
	"gitee.com/rcztcs/zjl/internal/zjl/types"
	"github.com/gin-gonic/gin"
)

// registerTaskRoutes 注册任务相关的路由
func registerTaskRoutes(r *gin.RouterGroup, svc *svc.ServiceContext) {
	tasks := r.Group("/tasks")
	{
		// 任务资源管理
		tasks.GET("", ListTasks(svc))                   // GET /tasks - 获取任务列表（支持多种筛选条件）
		tasks.GET("/:id", GetTaskByID(svc))             // GET /tasks/:id - 获取指定任务详情
		tasks.PUT("/:id/status", UpdateTaskStatus(svc)) // PUT /tasks/:id/status - 更新任务状态
	}

	// 创建任务的特殊路由
	task := r.Group("/task")
	{
		task.POST("/create", CreateTask(svc)) // POST /task/create - 创建任务
	}
}

// CreateTask 创建任务
func CreateTask(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req types.CreateTaskRequest

		err := utils.ParseRequest(c, &req)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		// TODO 获取文件

		// 企业信用码
		// TODO
		creditCode := "codeX1"

		// 调用服务层创建任务
		userID, password, err := svc.TaskService.CreateTask(req.ReportID, creditCode)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		res := map[string]any{
			"user_id":  userID,
			"password": password,
		}

		utils.ResponseSuccess(c, res)
	}
}

// GetTaskByID 根据ID获取任务详情
func GetTaskByID(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req types.GetTaskRequest

		err := utils.ParseRequest(c, &req)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		// 调用服务层获取任务详情
		task, err := svc.TaskService.GetTaskByID(req.ID)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		response := &types.GetTaskResponse{
			Task: task,
		}

		utils.ResponseSuccess(c, response)
	}
}

// ListTasks 获取任务列表（支持多种筛选条件）
func ListTasks(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req types.ListTasksRequest

		err := utils.ParseRequest(c, &req)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		tasks, total, err := svc.TaskService.ListTasks(req.Page, req.PageSize, req.UserID, req.Status, req.StartTime, req.EndTime)

		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		utils.ResponseList(c, tasks, total)
	}
}

// UpdateTaskStatus 更新任务状态
func UpdateTaskStatus(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req types.UpdateTaskStatusRequest
		err := utils.ParseRequest(c, &req)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		// 调用服务层更新任务状态
		err = svc.TaskService.UpdateTaskStatus(req.ID, req.Status)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		utils.ResponseSuccess(c, nil)
	}
}

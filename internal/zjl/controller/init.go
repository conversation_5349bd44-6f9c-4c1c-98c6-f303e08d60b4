package controller

import (
	"gitee.com/rcztcs/zjl/config"
	"gitee.com/rcztcs/zjl/internal/pkg/logger"
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"gitee.com/rcztcs/zjl/internal/zjl/svc"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func Init(addr string, conf *config.Config) error {
	gin.DefaultWriter = &ZapWriter{logger.Logger()}
	r := gin.Default()

	// 初始化数据库连接
	db := model.GetDB()

	serviceContext := svc.NewServiceContext(db, conf.JwtSecretKey, conf.UploadPath)

	initRouter(r.Group("/api"), serviceContext)

	go func() {
		if err := r.Run(addr); err != nil {
			logger.Logger().Error("web start faild", zap.Error(err))
			// TODO
			//_ = syscall.Kill(os.Getpid(), syscall.SIGTERM)
		}
	}()
	return nil
}

// ZapWriter 将 zap.Logger 适配为 io.Writer
type ZapWriter struct {
	logger *zap.Logger
}

func (w *ZapWriter) Write(p []byte) (n int, err error) {
	w.logger.Info(string(p)) // 将 Gin 的日志转发到 Zap
	return len(p), nil
}

func initRouter(r *gin.RouterGroup, ctx *svc.ServiceContext) {
	// 注册用户相关路由
	registerUserRoutes(r, ctx)

	// 注册角色相关路由
	registerRoleRoutes(r, ctx)

	// 注册权限相关路由
	registerPermissionRoutes(r, ctx)

	// 注册用户角色关联相关路由
	registerUserRoleRoutes(r, ctx)

	// 注册操作日志相关路由
	registerOperationLogRoutes(r, ctx)

	// 注册任务相关路由
	registerTaskRoutes(r, ctx)

	// 注册文件相关路由
	registerFileRoutes(r, ctx)
}

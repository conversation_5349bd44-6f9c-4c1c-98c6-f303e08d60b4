package model

import "gorm.io/gorm"

// TaskStatus 任务状态枚举
type TaskStatus string

const (
	// TaskStatusPending  待确认
	TaskStatusPending TaskStatus = "pending"
	// TaskStatusReviewing  审核中
	TaskStatusReviewing TaskStatus = "reviewing"
	// TaskStatusFinalReview  待终审
	TaskStatusFinalReview TaskStatus = "final_review"
	// TaskStatusCompleted 已完结
	TaskStatusCompleted TaskStatus = "completed"
)

// Task 任务模型
type Task struct {
	gorm.Model
	UserID   uint       `gorm:"column:user_id;type:int unsigned;not null;index" json:"user_id"` // 用户ID
	Status   TaskStatus `gorm:"column:status;type:varchar(20);not null;index" json:"status"`    // 状态
	ReportID string     `gorm:"column:report_id;type:varchar(100)" json:"report_id"`            // 报告ID
}

// GetStatusOrder 获取状态的顺序值，用于状态流转验证
func GetStatusOrder(status TaskStatus) int {
	switch status {
	case TaskStatusPending:
		return 1
	case TaskStatusReviewing:
		return 2
	case TaskStatusFinalReview:
		return 3
	case TaskStatusCompleted:
		return 4
	default:
		return 0
	}
}

// CanTransitionTo 检查是否可以从当前状态转换到目标状态
// 状态只能向前流转，不能倒退
func (t *Task) CanTransitionTo(targetStatus TaskStatus) bool {
	currentOrder := GetStatusOrder(t.Status)
	targetOrder := GetStatusOrder(targetStatus)

	// 状态顺序必须递增，不能倒退
	return targetOrder >= currentOrder
}
